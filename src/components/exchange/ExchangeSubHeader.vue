<template>
  <div class="sub-header flex-box">
    <div class="header-left flex-box space-between">
      <div class="flex-box">
        <BoxCoinIcon :icon="coinInfo.icon_url" class="icon-box"/>
        <ElPopover ref="popoverRef" trigger="click" popper-class="search-popover-cont-wrap" :width="460" popper-append-to-body>
          <template #reference>
            <div class="symbol-text cursor-pointer">
              <div class="symbol-title flex-box">
                <h2 style="white-space:nowrap;">{{ pair.replace('_', '/') }}</h2>
                <div class="arrow-down-box flex-box space-center m">
                  <MonoDownArrowMin size="16" class="fit-tc-primary" />
                </div>
                <div class="arrow-down-box flex-box space-center pc">
                  <MonoDownArrowMin size="16" class="fit-tc-primary" style="display:block;margin:0 auto;" />
                </div>
                <div class="collect-box flex-box space-center" @click.stop="selectCollect(pair, 'head')">
                  <MonoCollected v-if="favoriteMap[pair]" size="16" class="fit-theme" />
                  <MonoCollect v-else size="16" class="fit-tc-primary" />
                </div>
              </div>
              <p>{{ (ticker || {}).general_name }}</p>
            </div>
          </template>
          <ExchangeMarketsDropdown :pair="pair" :favoriteMap="favoriteMap" @selectCollect="selectCollect" @close="closePopover" @changePair="changePair" />
        </ElPopover>
      </div>
      <div class="collect-box mobile-item flex-box space-center" @click.stop="selectCollect(pair, 'head')">
        <MonoCollected v-if="favoriteMap[pair]" size="16" class="fit-theme" />
        <MonoCollect v-else size="24" class="fit-tc-primary" />
      </div>
    </div>
    <div class="pc-item">
      <div class="def-li">
        <span :class="className((ticker || {}).change)">{{ format((ticker || {}).last, priceScale, true) || '--' }}</span>
        <em>≈{{ useCommon.convert((ticker || {}).last, 'USDT') }}</em>
      </div> 
    </div>
    <BoxXOverflow class="pc-item">
      <div class="scroll-item">
        <span>{{ $t('24小时涨跌幅') }}</span>
        <em :class="Number((ticker || {}).change) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(((ticker || {}).change * 100), 2, true)  || '--' }}%</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24小时最高') }}</span>
        <em>{{ format((ticker || {}).high, priceScale, true) || '--' }}</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24小时最低') }}</span>
        <em>{{ format((ticker || {}).low, priceScale, true) || '--' }}</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24小时交易额(USDT)') }}</span>
        <em>{{ format((ticker || {}).amount, priceScale, true) || '--' }}</em>
      </div>
    </BoxXOverflow>
    <div class="header-right flex-box mobile-item align-start space-between">
      <div class="def-li mobile-item flex-box flex-column space-start align-start flex-1">
        <span :class="className((ticker || {}).change)">{{ (ticker || {}).last || '--' }}</span>
        <em>≈{{ useCommon.convert((ticker || {}).last, 'USDT') }}</em>
        <em :class="className((ticker || {}).change)">{{ format((ticker || {}).change * 100, 2, true) || '--' }} %</em>
      </div>
      <div class="flex-2">
        <div class="scroll-item">
          <span>{{ $t('24小时最高') }}</span>
          <em>{{ format((ticker || {}).high, priceScale, true) || '--' }}</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('24小时涨跌幅') }}</span>
          <em :class="Number((ticker || {}).change) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(((ticker || {}).change * 100), 2, true) || '--' }}%</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('24小时最低') }}</span>
          <em>{{ format((ticker || {}).low, priceScale, true) || '--' }}</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('24小时交易额(USDT)') }}</span>
          <em>{{ format((ticker || {}).amount, quantityScale, true) || '--' }}</em>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElPopover } from 'element-plus'
  import { imgDmain } from '~/config'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
  import ExchangeMarketsDropdown from './ExchangeMarketsDropdown.vue'
  import { favoriteAddOrDel, getFavoriteList } from '~/api/public'
  import { useCommonData } from '~/composables/index'
  import { usePairManager } from '~/composables/usePairManager'
  const { locale, t } = useI18n()

  // 使用新的交易对管理器
  const pairManager = usePairManager()

  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    ticker: {
      type: Object,
      default(){
        return {}
      }
    },
    priceScale: {
      type: [Number, String],
      default: ''
    },
    quantityScale: {
      type: [Number, String],
      default: ''
    }
  })
  const emit = defineEmits(['changePair'])
  const changePair = (pair) => {
    // 使用新的交易对管理器
    pairManager.changePair(pair, locale.value)
    emit('changePair', pair)
  }
  const className = (c) => {
    if (c * 1 < 0) {
      return 'fit-fall'
    } else {
      return 'fit-rise'
    }
  }
  const useCommon = useCommonData()
  const visible = ref(false)
  const isShowMenu = ref(false)
  const favoriteMap = ref({})
  const selectCollect = async(item, type='') => { // 取消或增加收藏
    const pairName = type === 'head' ? item : item.pair
    const isCurrentlyFavorite = pairManager.isFavorite(pairName)

    const { data, error } = await favoriteAddOrDel({
      add_str: isCurrentlyFavorite ? undefined : pairName,
      del_str: isCurrentlyFavorite ? pairName : undefined
    })

    if (data) {
      // 更新本地缓存
      if (isCurrentlyFavorite) {
        pairManager.removeFromFavorites(pairName)
        favoriteMap.value[pairName] = false
      } else {
        pairManager.addToFavorites(pairName)
        favoriteMap.value[pairName] = true
      }

      useCommon.showMsg('success', isCurrentlyFavorite ? t('取消收藏') : t('收藏成功'))
      await getCollectList()
    } else if (error.code * 1 === 2013) {
      useCommon.openLogin()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  // 获取collectList
  const getCollectList = async() => {
    const { data, error} = await getFavoriteList()
    if (data) {
      favoriteMap.value = {}
      const favorites = []
      data.forEach((item) => {
        favoriteMap.value[item.pair] = true
        favorites.push(item.pair)
      })
      // 缓存收藏列表
      pairManager.cacheFavoriteList(favorites)
    }
  }
  const popoverRef = ref(null)
  const closePopover = () => {
    if (popoverRef.value) {
      nextTick(() => {
        popoverRef.value.hide()
      })
    }
  }
  onMounted(() => {
    getCollectList()
  })
</script>
<style lang="scss">
.pc{
  display:flex;
}
.m{
  display:none;
}
.el-popover{
  &.search-popover-cont-wrap{
    padding:0 !important;
    height:calc(100% - 206px);
    min-height:460px;
  }
}
@include mb {
  .pc{
    display:none;
  }
  .m{
    display:block;
  }
}
</style>
