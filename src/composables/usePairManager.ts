import { ref, computed, nextTick, readonly } from 'vue'
import { storeToRefs } from 'pinia'
import { commonStore } from '~/stores/commonStore'
import { useCacheManager } from './useCacheManager'
import { debounce } from '~/utils'

interface PairChangeOptions {
  updateHistory?: boolean // 是否更新浏览器历史
  cancelOldSubscriptions?: boolean // 是否取消旧订阅
  subscribeNew?: boolean // 是否订阅新数据
}

/**
 * 交易对管理器
 * 统一管理交易对切换、币种选择、收藏功能等逻辑
 */
export function usePairManager() {
  const store = commonStore()
  const {
    pair,
    allPairList,
    pairInfo
  } = storeToRefs(store)

  const {
    cancelSocket,
    cleanupAllKlineSubscriptions,
    getDepthSocket,
    subTradesSocket,
    subTickerSocket,
    changePair: storeChangePair
  } = store
  
  // 缓存管理器
  const cacheManager = useCacheManager({
    defaultExpiry: 10 * 60 * 1000, // 10分钟
    storagePrefix: 'pair_cache_'
  })
  
  // 当前活跃的交易对
  const activePair = ref('')
  
  // 收藏列表缓存
  const favoriteListCache = ref<Set<string>>(new Set())
  
  // 防抖延迟
  const DEBOUNCE_DELAY = 200
  
  /**
   * 检查是否为合约交易对
   */
  const isSwapPair = (pairName: string): boolean => {
    return pairName.includes('_SWAP')
  }
  
  /**
   * 获取交易对的基础信息
   */
  const getPairInfo = (pairName: string) => {
    return pairInfo.value[pairName] || {}
  }
  
  /**
   * 缓存收藏列表
   */
  const cacheFavoriteList = (favorites: string[]) => {
    favoriteListCache.value = new Set(favorites)
    cacheManager.setCache('favorites', favorites, { persistent: true })
  }
  
  /**
   * 获取缓存的收藏列表
   */
  const getCachedFavoriteList = (): string[] => {
    const cached = cacheManager.getCache<string[]>('favorites')
    if (cached) {
      favoriteListCache.value = new Set(cached)
      return cached
    }
    return []
  }
  
  /**
   * 检查是否已收藏
   */
  const isFavorite = (pairName: string): boolean => {
    return favoriteListCache.value.has(pairName)
  }
  
  /**
   * 添加到收藏
   */
  const addToFavorites = (pairName: string) => {
    favoriteListCache.value.add(pairName)
    const favorites = Array.from(favoriteListCache.value)
    cacheManager.setCache('favorites', favorites, { persistent: true })
  }
  
  /**
   * 从收藏中移除
   */
  const removeFromFavorites = (pairName: string) => {
    favoriteListCache.value.delete(pairName)
    const favorites = Array.from(favoriteListCache.value)
    cacheManager.setCache('favorites', favorites, { persistent: true })
  }
  
  /**
   * 清理旧的订阅
   */
  const cleanupOldSubscriptions = (oldPair: string) => {
    if (oldPair) {
      // 清理WebSocket订阅
      cancelSocket(oldPair)
      // 清理K线订阅
      cleanupAllKlineSubscriptions()
    }
  }

  /**
   * 订阅新的数据
   */
  const subscribeNewData = (newPair: string) => {
    if (newPair) {
      // 订阅新的数据流
      subTickerSocket(newPair)
      subTradesSocket(newPair)
      getDepthSocket(newPair)
    }
  }
  
  /**
   * 更新浏览器历史
   */
  const updateBrowserHistory = (newPair: string, locale: string) => {
    const router = useRouter()
    const route = isSwapPair(newPair) ? 'future' : 'exchange'
    const newPath = `/${locale}/${route}/${newPair}`
    
    nextTick(() => {
      window.history.replaceState({}, '', newPath)
      if (router.currentRoute.value.params) {
        router.currentRoute.value.params.pair = newPair
      }
    })
  }
  
  /**
   * 防抖的交易对切换函数
   */
  const debouncedPairChange = debounce(async (
    newPair: string, 
    oldPair: string, 
    locale: string,
    options: PairChangeOptions = {}
  ) => {
    const {
      updateHistory = true,
      cancelOldSubscriptions = true,
      subscribeNew = true
    } = options
    
    try {
      // 1. 清理旧订阅
      if (cancelOldSubscriptions) {
        cleanupOldSubscriptions(oldPair)
      }
      
      // 2. 更新状态
      activePair.value = newPair
      pair.value = newPair
      
      // 3. 更新浏览器历史
      if (updateHistory) {
        updateBrowserHistory(newPair, locale)
      }
      
      // 4. 等待一个tick确保状态更新完成
      await nextTick()
      
      // 5. 订阅新数据
      if (subscribeNew) {
        subscribeNewData(newPair)
      }
      
    } catch (error) {
      console.error('Pair change error:', error)
    }
  }, DEBOUNCE_DELAY)
  
  /**
   * 切换交易对
   */
  const changePair = (
    newPair: string, 
    locale: string = 'zh',
    options: PairChangeOptions = {}
  ) => {
    const oldPair = activePair.value || pair.value
    
    // 如果是相同的交易对，不需要切换
    if (newPair === oldPair) {
      return
    }
    
    // 使用防抖切换
    debouncedPairChange(newPair, oldPair, locale, options)
  }
  
  /**
   * 快速切换交易对（跨页面）
   */
  const quickChangePair = (newPair: string, locale: string = 'zh') => {
    const router = useRouter()
    
    if (isSwapPair(newPair)) {
      router.push(`/${locale}/future/${newPair}`)
    } else {
      router.push(`/${locale}/exchange/${newPair}`)
    }
  }
  
  /**
   * 搜索交易对
   */
  const searchPairs = (query: string): any[] => {
    if (!query) return allPairList.value
    
    const searchTerm = query.toUpperCase()
    return allPairList.value.filter(pair => 
      pair.toUpperCase().includes(searchTerm)
    )
  }
  
  /**
   * 按分类获取交易对
   */
  const getPairsByCategory = (category: string): any[] => {
    return allPairList.value.filter(pair => {
      if (category === 'spot') return !isSwapPair(pair)
      if (category === 'futures') return isSwapPair(pair)
      if (category === 'favorites') return isFavorite(pair)
      return true
    })
  }
  
  /**
   * 获取热门交易对
   */
  const getPopularPairs = (): any[] => {
    // 这里可以根据实际业务逻辑返回热门交易对
    const popular = ['BTC_USDT', 'ETH_USDT', 'BNB_USDT', 'ADA_USDT']
    return allPairList.value.filter(pair => 
      popular.some(p => pair.includes(p.split('_')[0]))
    )
  }
  
  /**
   * 清理所有缓存
   */
  const clearAllCache = () => {
    cacheManager.clearAllCache()
    favoriteListCache.value.clear()
  }
  
  /**
   * 获取管理器状态
   */
  const getManagerStatus = () => {
    return {
      activePair: activePair.value,
      favoritesCount: favoriteListCache.value.size,
      cacheStats: cacheManager.getCacheStats()
    }
  }
  
  return {
    // 状态
    activePair: readonly(activePair),
    favoriteListCache: readonly(favoriteListCache),
    
    // 交易对操作
    changePair,
    quickChangePair,
    isSwapPair,
    getPairInfo,
    
    // 收藏功能
    isFavorite,
    addToFavorites,
    removeFromFavorites,
    cacheFavoriteList,
    getCachedFavoriteList,
    
    // 搜索和分类
    searchPairs,
    getPairsByCategory,
    getPopularPairs,
    
    // 工具方法
    clearAllCache,
    getManagerStatus,
    
    // 响应式计算属性
    pairCount: computed(() => allPairList.value.length),
    favoritesCount: computed(() => favoriteListCache.value.size)
  }
}
