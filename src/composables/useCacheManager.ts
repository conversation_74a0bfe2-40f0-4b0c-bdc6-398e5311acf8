import { ref, computed } from 'vue'
import { setStorage, getStorage, removeLocalStorage } from '~/utils'

interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry?: number
}

interface CacheConfig {
  defaultExpiry?: number // 默认过期时间（毫秒）
  maxSize?: number // 最大缓存条目数
  storagePrefix?: string // localStorage前缀
}

/**
 * 缓存管理器
 * 统一管理各种数据缓存，包括内存缓存和localStorage缓存
 */
export function useCacheManager(config: CacheConfig = {}) {
  const {
    defaultExpiry = 5 * 60 * 1000, // 默认5分钟
    maxSize = 100,
    storagePrefix = 'cache_'
  } = config

  // 内存缓存
  const memoryCache = ref<Map<string, CacheItem>>(new Map())
  
  /**
   * 检查缓存是否过期
   */
  const isExpired = (item: CacheItem): boolean => {
    if (!item.expiry) return false
    return Date.now() > item.timestamp + item.expiry
  }
  
  /**
   * 清理过期的内存缓存
   */
  const cleanupExpiredMemoryCache = () => {
    const now = Date.now()
    for (const [key, item] of memoryCache.value) {
      if (isExpired(item)) {
        memoryCache.value.delete(key)
      }
    }
  }
  
  /**
   * 限制缓存大小
   */
  const limitCacheSize = () => {
    if (memoryCache.value.size > maxSize) {
      // 删除最旧的条目
      const entries = Array.from(memoryCache.value.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
      
      const toDelete = entries.slice(0, memoryCache.value.size - maxSize)
      toDelete.forEach(([key]) => {
        memoryCache.value.delete(key)
      })
    }
  }
  
  /**
   * 设置内存缓存
   */
  const setMemoryCache = <T>(key: string, data: T, expiry?: number): void => {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: expiry || defaultExpiry
    }
    
    memoryCache.value.set(key, item)
    limitCacheSize()
  }
  
  /**
   * 获取内存缓存
   */
  const getMemoryCache = <T>(key: string): T | null => {
    const item = memoryCache.value.get(key)
    if (!item) return null
    
    if (isExpired(item)) {
      memoryCache.value.delete(key)
      return null
    }
    
    return item.data as T
  }
  
  /**
   * 删除内存缓存
   */
  const deleteMemoryCache = (key: string): void => {
    memoryCache.value.delete(key)
  }
  
  /**
   * 清空内存缓存
   */
  const clearMemoryCache = (): void => {
    memoryCache.value.clear()
  }
  
  /**
   * 设置localStorage缓存
   */
  const setStorageCache = <T>(key: string, data: T, expiry?: number): void => {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: expiry || defaultExpiry
    }
    
    setStorage(storagePrefix + key, item)
  }
  
  /**
   * 获取localStorage缓存
   */
  const getStorageCache = <T>(key: string): T | null => {
    const item = getStorage(storagePrefix + key) as CacheItem<T>
    if (!item) return null
    
    if (isExpired(item)) {
      removeLocalStorage(storagePrefix + key)
      return null
    }
    
    return item.data
  }
  
  /**
   * 删除localStorage缓存
   */
  const deleteStorageCache = (key: string): void => {
    removeLocalStorage(storagePrefix + key)
  }
  
  /**
   * 设置缓存（优先内存，可选择同时存储到localStorage）
   */
  const setCache = <T>(
    key: string, 
    data: T, 
    options: {
      expiry?: number
      persistent?: boolean // 是否同时存储到localStorage
    } = {}
  ): void => {
    const { expiry, persistent = false } = options
    
    setMemoryCache(key, data, expiry)
    
    if (persistent) {
      setStorageCache(key, data, expiry)
    }
  }
  
  /**
   * 获取缓存（优先内存，fallback到localStorage）
   */
  const getCache = <T>(key: string): T | null => {
    // 先尝试内存缓存
    let data = getMemoryCache<T>(key)
    if (data !== null) return data
    
    // 再尝试localStorage缓存
    data = getStorageCache<T>(key)
    if (data !== null) {
      // 将localStorage数据加载到内存缓存
      setMemoryCache(key, data)
      return data
    }
    
    return null
  }
  
  /**
   * 删除缓存（同时删除内存和localStorage）
   */
  const deleteCache = (key: string): void => {
    deleteMemoryCache(key)
    deleteStorageCache(key)
  }
  
  /**
   * 清空所有缓存
   */
  const clearAllCache = (): void => {
    clearMemoryCache()
    
    // 清理localStorage中的缓存
    if (typeof window !== 'undefined' && window.localStorage) {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(storagePrefix)) {
          removeLocalStorage(key.replace(storagePrefix, ''))
        }
      })
    }
  }
  
  /**
   * 获取缓存统计信息
   */
  const getCacheStats = () => {
    return {
      memorySize: memoryCache.value.size,
      maxSize,
      storagePrefix
    }
  }
  
  // 定期清理过期缓存
  if (typeof window !== 'undefined') {
    setInterval(cleanupExpiredMemoryCache, 60000) // 每分钟清理一次
  }
  
  return {
    // 内存缓存方法
    setMemoryCache,
    getMemoryCache,
    deleteMemoryCache,
    clearMemoryCache,
    
    // localStorage缓存方法
    setStorageCache,
    getStorageCache,
    deleteStorageCache,
    
    // 统一缓存方法
    setCache,
    getCache,
    deleteCache,
    clearAllCache,
    
    // 工具方法
    getCacheStats,
    cleanupExpiredMemoryCache,
    
    // 响应式状态
    cacheSize: computed(() => memoryCache.value.size)
  }
}
