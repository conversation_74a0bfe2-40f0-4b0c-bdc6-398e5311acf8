import { ref, onMounted, onUnmounted, onBeforeUnmount, readonly } from 'vue'

interface CleanupTask {
  id: string
  cleanup: () => void
  priority?: number // 清理优先级，数字越小优先级越高
}

interface EventListenerTask {
  element: EventTarget
  event: string
  handler: EventListener
  options?: boolean | AddEventListenerOptions
}

/**
 * 生命周期管理器
 * 统一管理组件的资源清理，避免内存泄漏
 */
export function useLifecycleManager() {
  // 清理任务列表
  const cleanupTasks = ref<Map<string, CleanupTask>>(new Map())
  
  // 事件监听器列表
  const eventListeners = ref<EventListenerTask[]>([])
  
  // 定时器列表
  const timers = ref<Set<NodeJS.Timeout>>(new Set())
  
  // 动画帧ID列表
  const animationFrames = ref<Set<number>>(new Set())
  
  // WebSocket连接列表
  const webSockets = ref<Set<WebSocket>>(new Set())
  
  // 是否已经清理
  const isCleanedUp = ref(false)
  
  /**
   * 添加清理任务
   */
  const addCleanupTask = (id: string, cleanup: () => void, priority: number = 100) => {
    if (isCleanedUp.value) {
      console.warn(`Cannot add cleanup task '${id}' after cleanup`)
      return
    }
    
    cleanupTasks.value.set(id, { id, cleanup, priority })
  }
  
  /**
   * 移除清理任务
   */
  const removeCleanupTask = (id: string) => {
    cleanupTasks.value.delete(id)
  }
  
  /**
   * 添加事件监听器
   */
  const addEventListener = (
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    if (isCleanedUp.value) {
      console.warn(`Cannot add event listener for '${event}' after cleanup`)
      return
    }
    
    element.addEventListener(event, handler, options)
    eventListeners.value.push({ element, event, handler, options })
  }
  
  /**
   * 移除事件监听器
   */
  const removeEventListener = (
    element: EventTarget,
    event: string,
    handler: EventListener
  ) => {
    element.removeEventListener(event, handler)
    
    const index = eventListeners.value.findIndex(
      listener => 
        listener.element === element && 
        listener.event === event && 
        listener.handler === handler
    )
    
    if (index > -1) {
      eventListeners.value.splice(index, 1)
    }
  }
  
  /**
   * 添加定时器
   */
  const addTimer = (timer: NodeJS.Timeout) => {
    if (isCleanedUp.value) {
      console.warn('Cannot add timer after cleanup')
      clearTimeout(timer)
      return
    }
    
    timers.value.add(timer)
  }
  
  /**
   * 移除定时器
   */
  const removeTimer = (timer: NodeJS.Timeout) => {
    clearTimeout(timer)
    timers.value.delete(timer)
  }
  
  /**
   * 创建安全的setTimeout
   */
  const safeSetTimeout = (callback: () => void, delay: number): NodeJS.Timeout => {
    const timer = setTimeout(() => {
      callback()
      timers.value.delete(timer)
    }, delay)
    
    addTimer(timer)
    return timer
  }
  
  /**
   * 创建安全的setInterval
   */
  const safeSetInterval = (callback: () => void, delay: number): NodeJS.Timeout => {
    const timer = setInterval(callback, delay)
    addTimer(timer)
    return timer
  }
  
  /**
   * 添加动画帧
   */
  const addAnimationFrame = (frameId: number) => {
    if (isCleanedUp.value) {
      console.warn('Cannot add animation frame after cleanup')
      cancelAnimationFrame(frameId)
      return
    }
    
    animationFrames.value.add(frameId)
  }
  
  /**
   * 移除动画帧
   */
  const removeAnimationFrame = (frameId: number) => {
    cancelAnimationFrame(frameId)
    animationFrames.value.delete(frameId)
  }
  
  /**
   * 创建安全的requestAnimationFrame
   */
  const safeRequestAnimationFrame = (callback: FrameRequestCallback): number => {
    const frameId = requestAnimationFrame((time) => {
      callback(time)
      animationFrames.value.delete(frameId)
    })
    
    addAnimationFrame(frameId)
    return frameId
  }
  
  /**
   * 添加WebSocket连接
   */
  const addWebSocket = (ws: WebSocket) => {
    if (isCleanedUp.value) {
      console.warn('Cannot add WebSocket after cleanup')
      ws.close()
      return
    }
    
    webSockets.value.add(ws)
  }
  
  /**
   * 移除WebSocket连接
   */
  const removeWebSocket = (ws: WebSocket) => {
    if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
      ws.close()
    }
    webSockets.value.delete(ws)
  }
  
  /**
   * 清理所有事件监听器
   */
  const cleanupEventListeners = () => {
    eventListeners.value.forEach(({ element, event, handler }) => {
      try {
        element.removeEventListener(event, handler)
      } catch (error) {
        console.warn('Failed to remove event listener:', error)
      }
    })
    eventListeners.value = []
  }
  
  /**
   * 清理所有定时器
   */
  const cleanupTimers = () => {
    timers.value.forEach(timer => {
      try {
        clearTimeout(timer)
        clearInterval(timer)
      } catch (error) {
        console.warn('Failed to clear timer:', error)
      }
    })
    timers.value.clear()
  }
  
  /**
   * 清理所有动画帧
   */
  const cleanupAnimationFrames = () => {
    animationFrames.value.forEach(frameId => {
      try {
        cancelAnimationFrame(frameId)
      } catch (error) {
        console.warn('Failed to cancel animation frame:', error)
      }
    })
    animationFrames.value.clear()
  }
  
  /**
   * 清理所有WebSocket连接
   */
  const cleanupWebSockets = () => {
    webSockets.value.forEach(ws => {
      try {
        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
          ws.close()
        }
      } catch (error) {
        console.warn('Failed to close WebSocket:', error)
      }
    })
    webSockets.value.clear()
  }
  
  /**
   * 执行所有清理任务
   */
  const executeCleanupTasks = () => {
    // 按优先级排序
    const sortedTasks = Array.from(cleanupTasks.value.values())
      .sort((a, b) => (a.priority || 100) - (b.priority || 100))
    
    sortedTasks.forEach(task => {
      try {
        task.cleanup()
      } catch (error) {
        console.warn(`Failed to execute cleanup task '${task.id}':`, error)
      }
    })
    
    cleanupTasks.value.clear()
  }
  
  /**
   * 执行完整清理
   */
  const cleanup = () => {
    if (isCleanedUp.value) {
      return
    }
    
    isCleanedUp.value = true
    
    // 按顺序清理资源
    executeCleanupTasks()
    cleanupEventListeners()
    cleanupTimers()
    cleanupAnimationFrames()
    cleanupWebSockets()
  }
  
  /**
   * 获取管理器状态
   */
  const getStatus = () => {
    return {
      cleanupTasksCount: cleanupTasks.value.size,
      eventListenersCount: eventListeners.value.length,
      timersCount: timers.value.size,
      animationFramesCount: animationFrames.value.size,
      webSocketsCount: webSockets.value.size,
      isCleanedUp: isCleanedUp.value
    }
  }
  
  // 自动在组件卸载时清理
  onBeforeUnmount(() => {
    cleanup()
  })
  
  return {
    // 清理任务管理
    addCleanupTask,
    removeCleanupTask,
    
    // 事件监听器管理
    addEventListener,
    removeEventListener,
    
    // 定时器管理
    addTimer,
    removeTimer,
    safeSetTimeout,
    safeSetInterval,
    
    // 动画帧管理
    addAnimationFrame,
    removeAnimationFrame,
    safeRequestAnimationFrame,
    
    // WebSocket管理
    addWebSocket,
    removeWebSocket,
    
    // 手动清理
    cleanup,
    
    // 状态查询
    getStatus,
    isCleanedUp: readonly(isCleanedUp)
  }
}
