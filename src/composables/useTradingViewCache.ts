import { ref, computed } from 'vue'
import { useCacheManager } from './useCacheManager'

interface KlineData {
  time: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

interface CacheKey {
  symbol: string
  resolution: string
  from?: number
  to?: number
  firstDataRequest?: boolean
}

/**
 * TradingView数据缓存管理器
 * 专门管理K线历史数据的缓存
 */
export function useTradingViewCache() {
  const cacheManager = useCacheManager({
    defaultExpiry: 30 * 1000, // 30秒过期
    maxSize: 50,
    storagePrefix: 'tv_cache_'
  })
  
  // 最后完整K线数据缓存
  const lastCompleteBarCache = ref<Map<string, any>>(new Map())
  
  // 订阅映射缓存
  const subscriptionMapCache = ref<Map<string, any>>(new Map())
  
  /**
   * 生成缓存键
   */
  const generateCacheKey = (params: CacheKey): string => {
    const { symbol, resolution, from, to, firstDataRequest } = params
    if (firstDataRequest) {
      return `${symbol}-${resolution}-first`
    }
    return `${symbol}-${resolution}-${from || 'latest'}`
  }
  
  /**
   * 缓存K线历史数据
   */
  const cacheHistoricalData = (
    params: CacheKey,
    data: KlineData[],
    options: {
      expiry?: number
      persistent?: boolean
    } = {}
  ): void => {
    const cacheKey = generateCacheKey(params)
    const { expiry = 30 * 1000, persistent = false } = options
    
    // 月线数据缓存时间更长
    const isMonthlyResolution = params.resolution === '1M'
    const finalExpiry = isMonthlyResolution 
      ? (params.firstDataRequest ? 60 * 1000 : 30 * 1000)
      : expiry
    
    cacheManager.setCache(cacheKey, data, {
      expiry: finalExpiry,
      persistent
    })
  }
  
  /**
   * 获取K线历史数据缓存
   */
  const getHistoricalDataCache = (params: CacheKey): KlineData[] | null => {
    const cacheKey = generateCacheKey(params)
    return cacheManager.getCache<KlineData[]>(cacheKey)
  }
  
  /**
   * 删除K线历史数据缓存
   */
  const deleteHistoricalDataCache = (params: CacheKey): void => {
    const cacheKey = generateCacheKey(params)
    cacheManager.deleteCache(cacheKey)
  }
  
  /**
   * 设置最后完整K线数据
   */
  const setLastCompleteBar = (key: string, data: any): void => {
    lastCompleteBarCache.value.set(key, data)
  }
  
  /**
   * 获取最后完整K线数据
   */
  const getLastCompleteBar = (key: string): any => {
    return lastCompleteBarCache.value.get(key)
  }
  
  /**
   * 删除最后完整K线数据
   */
  const deleteLastCompleteBar = (key: string): void => {
    lastCompleteBarCache.value.delete(key)
  }
  
  /**
   * 清理最后完整K线数据（保留指定key）
   */
  const cleanupLastCompleteBar = (keepKey?: string): void => {
    if (keepKey) {
      const keepData = lastCompleteBarCache.value.get(keepKey)
      lastCompleteBarCache.value.clear()
      if (keepData) {
        lastCompleteBarCache.value.set(keepKey, keepData)
      }
    } else {
      lastCompleteBarCache.value.clear()
    }
  }
  
  /**
   * 设置订阅映射
   */
  const setSubscriptionMap = (key: string, data: any): void => {
    subscriptionMapCache.value.set(key, data)
  }
  
  /**
   * 获取订阅映射
   */
  const getSubscriptionMap = (key: string): any => {
    return subscriptionMapCache.value.get(key)
  }
  
  /**
   * 删除订阅映射
   */
  const deleteSubscriptionMap = (key: string): void => {
    subscriptionMapCache.value.delete(key)
  }
  
  /**
   * 清空订阅映射
   */
  const clearSubscriptionMap = (): void => {
    subscriptionMapCache.value.clear()
  }
  
  /**
   * 强制刷新缓存
   */
  const forceRefresh = (): void => {
    cacheManager.clearMemoryCache()
    lastCompleteBarCache.value.clear()
  }
  
  /**
   * 检查缓存是否存在且有效
   */
  const isCacheValid = (params: CacheKey): boolean => {
    const data = getHistoricalDataCache(params)
    return data !== null && Array.isArray(data) && data.length > 0
  }
  
  /**
   * 获取缓存统计信息
   */
  const getCacheStats = () => {
    return {
      ...cacheManager.getCacheStats(),
      lastCompleteBarSize: lastCompleteBarCache.value.size,
      subscriptionMapSize: subscriptionMapCache.value.size
    }
  }
  
  /**
   * 清理所有缓存
   */
  const clearAllCache = (): void => {
    cacheManager.clearAllCache()
    lastCompleteBarCache.value.clear()
    subscriptionMapCache.value.clear()
  }
  
  /**
   * 预加载常用周期数据
   */
  const preloadCommonPeriods = async (
    symbol: string,
    periods: string[] = ['1m', '5m', '15m', '1h', '4h', '1d']
  ): Promise<void> => {
    // 这里可以实现预加载逻辑
    // 暂时留空，后续可以根据需要实现
  }
  
  return {
    // 历史数据缓存
    cacheHistoricalData,
    getHistoricalDataCache,
    deleteHistoricalDataCache,
    isCacheValid,
    
    // 最后完整K线数据
    setLastCompleteBar,
    getLastCompleteBar,
    deleteLastCompleteBar,
    cleanupLastCompleteBar,
    
    // 订阅映射
    setSubscriptionMap,
    getSubscriptionMap,
    deleteSubscriptionMap,
    clearSubscriptionMap,
    
    // 工具方法
    forceRefresh,
    getCacheStats,
    clearAllCache,
    preloadCommonPeriods,
    
    // 响应式状态
    cacheSize: computed(() => cacheManager.cacheSize.value),
    lastCompleteBarSize: computed(() => lastCompleteBarCache.value.size),
    subscriptionMapSize: computed(() => subscriptionMapCache.value.size)
  }
}
