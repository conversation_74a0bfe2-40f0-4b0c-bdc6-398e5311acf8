import { storeToRefs } from "pinia"
import { ref, watch } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'
import { useTradingViewCache } from './useTradingViewCache'

export default function useDatafeedAction(info) {
  // 使用新的缓存管理器
  const tradingViewCache = useTradingViewCache()
  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineTicker, ticker } = storeToRefs(store)
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  let rafId: number | null = null

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  let key = ''
  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    console.log(symbolInfo, 'symbolInfoGetBars')
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]
    key = `${symbolInfo.fullName}-${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围
    // 获取历史数据
    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = () => {
    dataCache.clear()
    tradingViewCache.clearAllCache()
    lastCompleteBar.value = {}
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      clearCache()
      tradingViewCache.forceRefresh()
    }
  }
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    // 使用新的缓存管理器
    const cacheParams = { symbol, resolution, from, to, firstDataRequest }

    // 检查缓存
    if (firstDataRequest && !forceRefresh.value) {
      const cachedData = tradingViewCache.getHistoricalDataCache(cacheParams)
      if (cachedData && cachedData.length > 0) {
        onHistoryCallback(cachedData, { noData: false })
        return
      }
    }

    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    try {
      const now = Date.now()
      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: firstDataRequest ? now : preObj.value.time,
        limit: countBack > 300 ? 300 : countBack
      })

      if (data) {
        const formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            // 使用新的缓存管理器缓存数据
            tradingViewCache.cacheHistoricalData(cacheParams, formattedData, {
              persistent: true // 持久化到localStorage
            })
          }
        }

        onHistoryCallback(formattedData, { noData: formattedData.length === 0 })
      } else {
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      onErrorCallback(error)
    }
  }
  let lastCompleteBar = ref({})

  watch([ticker, klineTicker], ([val1, val2]) => {
    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    const subscription = tradingViewCache.getSubscriptionMap(key)
    if (subscription && last && formatSymbol(subscription.symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subscription.symbol) === val2.currentPair &&
          interval.value === val2.currentPeriod) {
        resultVal = {
          time: Number(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        // 使用新的缓存管理器
        tradingViewCache.setLastCompleteBar(key, {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        })
      } else if (val2 && val2.currentPair && formatSymbol(subscription.symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: Number(val2.time) || Date.now(),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (tradingViewCache.getLastCompleteBar(key)) {
        const baseBar = tradingViewCache.getLastCompleteBar(key)
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: Date.now(),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      rafId = requestAnimationFrame(() => {
        const currentSubscription = tradingViewCache.getSubscriptionMap(key)
        if (currentSubscription && currentSubscription.listen) {
          currentSubscription.listen(resultVal)
        }
        rafId = null
      })
    }
  }, { deep: true })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`

    // 使用新的缓存管理器管理订阅
    if (tradingViewCache.getSubscriptionMap(subscriptionKey)) {
      tradingViewCache.deleteSubscriptionMap(subscriptionKey)
    }
    if (tradingViewCache.getSubscriptionMap(subscriberUID)) {
      const oldKey = tradingViewCache.getSubscriptionMap(subscriberUID)
      if (typeof oldKey === 'string') {
        tradingViewCache.deleteSubscriptionMap(oldKey)
      }
      tradingViewCache.deleteSubscriptionMap(subscriberUID)
    }

    const currentKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    // 清理旧的lastCompleteBar数据，保留当前的
    tradingViewCache.cleanupLastCompleteBar(currentKey)

    // 使用新的缓存管理器存储订阅信息
    tradingViewCache.setSubscriptionMap(subscriptionKey, {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        onRealtimeCallback(newPriceData)
      }
    })

    tradingViewCache.setSubscriptionMap(subscriberUID, subscriptionKey)
  }

  return {
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: 'Asia/Shanghai',
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      console.log(pairInfo, symbolInfo, 'symbolInforesolveSymbol')
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      // 使用新的缓存管理器清理订阅
      const subscriptionKey = tradingViewCache.getSubscriptionMap(subscriberUID)
      if (subscriptionKey && typeof subscriptionKey === 'string') {
        tradingViewCache.deleteSubscriptionMap(subscriptionKey)
      }
      tradingViewCache.deleteSubscriptionMap(subscriberUID)
    },
    clearCache,
    setForceRefresh
  }
}