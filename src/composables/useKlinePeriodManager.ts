import { ref, nextTick, readonly } from 'vue'
import { storeToRefs } from 'pinia'
import { commonStore } from '~/stores/commonStore'
import { debounce, socket } from '~/utils'

interface PeriodSubscription {
  pair: string
  period: string
  isSubscribing: boolean
  isSubscribed: boolean
  callback?: Function
  eventName?: string
}

/**
 * K线周期管理器
 * 统一管理K线周期切换逻辑，解决重复订阅、监听器累积、数据竞态等问题
 */
export function useKlinePeriodManager() {
  const store = commonStore()
  const { klineList, klineTicker } = storeToRefs(store)
  
  // 当前活跃的订阅状态
  const activeSubscriptions = ref<Map<string, PeriodSubscription>>(new Map())
  
  // 监听器回调映射
  const listenerCallbacks = ref<Map<string, Function>>(new Map())
  
  // 防抖延迟时间
  const DEBOUNCE_DELAY = 300
  
  /**
   * 生成订阅键
   */
  const generateSubscriptionKey = (pair: string, period: string): string => {
    return `${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${period}`
  }
  
  /**
   * 清理指定的监听器
   */
  const cleanupListener = (eventName: string) => {
    const callback = listenerCallbacks.value.get(eventName)
    if (callback && socket?.off) {
      socket.off(eventName, callback)
      listenerCallbacks.value.delete(eventName)
    }
  }
  
  /**
   * 清理指定的订阅
   */
  const cleanupSubscription = (subscriptionKey: string) => {
    const subscription = activeSubscriptions.value.get(subscriptionKey)
    if (subscription) {
      // 发送取消订阅消息
      if (socket?.send) {
        socket.send({
          "method": "UNSUBSCRIBE",
          "params": [subscriptionKey]
        })
      }
      
      // 清理监听器
      if (subscription.eventName) {
        cleanupListener(subscription.eventName)
      }
      
      // 移除订阅记录
      activeSubscriptions.value.delete(subscriptionKey)
    }
  }
  
  /**
   * 订阅K线数据
   */
  const subscribeKline = async (pair: string, period: string): Promise<boolean> => {
    const subscriptionKey = generateSubscriptionKey(pair, period)
    
    // 检查是否已经在订阅中
    const existingSubscription = activeSubscriptions.value.get(subscriptionKey)
    if (existingSubscription?.isSubscribing || existingSubscription?.isSubscribed) {
      console.log(`Already subscribing/subscribed to ${subscriptionKey}`)
      return true
    }
    
    return new Promise((resolve, reject) => {
      try {
        // 标记为订阅中
        const subscription: PeriodSubscription = {
          pair,
          period,
          isSubscribing: true,
          isSubscribed: false,
          eventName: subscriptionKey
        }
        activeSubscriptions.value.set(subscriptionKey, subscription)
        
        // 创建回调函数
        const callback = (res: any) => {
          try {
            const currentSubscription = activeSubscriptions.value.get(subscriptionKey)
            if (!currentSubscription) return // 订阅已被取消
            
            if (res.t === 0) { // 全量数据
              klineList.value = res.d
            }

            if (res.d && res.d.length) {
              klineTicker.value = {
                ...res.d[res.d.length - 1],
                currentPair: res.stream.split('.')[1],
                currentPeriod: period,
              }
            }
            
            // 标记为已订阅
            if (currentSubscription.isSubscribing) {
              currentSubscription.isSubscribing = false
              currentSubscription.isSubscribed = true
              activeSubscriptions.value.set(subscriptionKey, currentSubscription)
              resolve(true)
            }
          } catch (error) {
            console.error('Kline callback error:', error)
            reject(error)
          }
        }
        
        // 保存回调引用
        listenerCallbacks.value.set(subscriptionKey, callback)
        
        // 添加监听器
        if (socket?.on) {
          socket.on(subscriptionKey, callback)
        }

        // 发送订阅请求
        if (socket?.send) {
          socket.send({
            "method": "SUBSCRIBE",
            "params": [subscriptionKey]
          })
        }
        
        // 设置超时处理
        setTimeout(() => {
          const currentSubscription = activeSubscriptions.value.get(subscriptionKey)
          if (currentSubscription?.isSubscribing) {
            console.warn(`Subscription timeout for ${subscriptionKey}`)
            cleanupSubscription(subscriptionKey)
            reject(new Error('Subscription timeout'))
          }
        }, 10000) // 10秒超时
        
      } catch (error) {
        console.error('Subscribe kline error:', error)
        cleanupSubscription(subscriptionKey)
        reject(error)
      }
    })
  }
  
  /**
   * 取消订阅K线数据
   */
  const unsubscribeKline = (pair: string, period: string) => {
    const subscriptionKey = generateSubscriptionKey(pair, period)
    cleanupSubscription(subscriptionKey)
  }
  
  /**
   * 防抖的周期切换函数
   */
  const debouncedPeriodChange = debounce(async (pair: string, newPeriod: string, oldPeriod?: string) => {
    try {
      // 先取消旧订阅
      if (oldPeriod && oldPeriod !== newPeriod) {
        unsubscribeKline(pair, oldPeriod)
      }
      
      // 等待一个tick确保清理完成
      await nextTick()
      
      // 订阅新周期
      if (newPeriod) {
        await subscribeKline(pair, newPeriod)
      }
    } catch (error) {
      console.error('Period change error:', error)
    }
  }, DEBOUNCE_DELAY)
  
  /**
   * 切换周期
   */
  const changePeriod = (pair: string, newPeriod: string, oldPeriod?: string) => {
    debouncedPeriodChange(pair, newPeriod, oldPeriod)
  }
  
  /**
   * 清理所有订阅
   */
  const cleanupAll = () => {
    // 清理所有订阅
    for (const [key] of activeSubscriptions.value) {
      cleanupSubscription(key)
    }
    
    // 清理所有监听器
    for (const [eventName] of listenerCallbacks.value) {
      cleanupListener(eventName)
    }
    
    activeSubscriptions.value.clear()
    listenerCallbacks.value.clear()
  }
  
  /**
   * 获取当前订阅状态
   */
  const getSubscriptionStatus = (pair: string, period: string) => {
    const subscriptionKey = generateSubscriptionKey(pair, period)
    return activeSubscriptions.value.get(subscriptionKey)
  }
  
  /**
   * 检查是否已订阅
   */
  const isSubscribed = (pair: string, period: string): boolean => {
    const subscription = getSubscriptionStatus(pair, period)
    return subscription?.isSubscribed || false
  }
  
  return {
    subscribeKline,
    unsubscribeKline,
    changePeriod,
    cleanupAll,
    getSubscriptionStatus,
    isSubscribed,
    activeSubscriptions: readonly(activeSubscriptions)
  }
}
