import locales from './src/locales'
import { BASE_URL_DIRECTORY, SITE_NAME, port } from './src/config'
import { defineConfig } from 'vite'

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineConfig({
  ssr:false,
  vite: {
    server: {
      proxy: {
        [`/api`]: {
          target: 'https://api.ktx.one',
          changeOrigin: true,
          secure: false
        }
      },
      allowedHosts: ['test.tonetou.com', 'test.ktx.one', 'test.ktx.com']
    },
    plugins: [
      { src: '@/plugins/common.ts', ssr: true }
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          additionalData: '@import "@/assets/style/mixin.scss";',
        },
      },
    },
    define: {
      global: 'globalThis',
    },
    build: {
      sourcemap: false,
      rollupOptions: {
        output:{
            manualChunks(id) {
                if (id.includes('node_modules')) {
                    return id.toString().split('node_modules/')[1].split('/')[0].toString();
                }
            }
        }
      },
      treeshake: true, // 移除未使用的代码
      minify: 'terser', // 使用 Terser 进行代码压缩
      terserOptions: {
        compress: {
          drop_console: true, // 移除 console 语句
        },
      },
      target: ['es2015', 'ios12', 'chrome87', 'safari12']
    }
  },
  experimental: {
    swc: true, // 使用 Rust-based SWC 转译（比 Babel 更高效）
  },
  runtimeConfig: {
    // 以下变量只能在服务端使用
    apiSecret: '',
    apiKey: '',
    testVariable: '',
    // testTestVariable:'', // 变量必须在nuxt.config.js中声明才可以使用
    // public 下的变量可以在客户端和服务端使用
    public: {
      apiBase: '',
    }
  },

  devServer: {
    port,
    host: '127.0.0.1',
  },

  modules: [
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    '@pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/i18n',
    "@element-plus/nuxt",
    'nuxt-swiper',
    { importStyle: false }
  ],

  i18n: {
    locales,
    defaultLocale: 'en',
    detectBrowserLanguage: { 
      alwaysRedirect: true, 
      fallbackLocale: 'en', 
      redirectOn: 'root', 
      useCookie: true, 
      cookieCrossOrigin: false, 
      cookieDomain: null, 
      cookieKey: `locale`, 
      cookieSecure: false 
    },
    pages: {
      'google-callback': {
        'en': false,
        'zh': false,
        'ko': false,
        'ja': false,
        'zh-HANT': false
      },
      'appleid-callback': {
        'en': false,
        'zh': false,
        'ko': false,
        'ja': false,
        'zh-HANT': false
      }
    },
    compilation: {
      strictMessage: false,
    },
    strategy: 'prefix',
  },

  piniaPersistedstate: {
    storage: 'localStorage'
  },

  colorMode: {
    preference: 'light', // default value of $colorMode.preference
    fallback: 'light',
    classSuffix: '',
  },

  devtools: { enabled: false },

  app: {
    baseURL: BASE_URL_DIRECTORY,
    buildAssetsDir: 'ktx-web-ssr',
    cdnURL: 'https://res.ktx.com/',
    head: {
      meta: [
        {
          name: 'apple-mobile-web-app-capable',
          content: 'yes'
        },
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no'
        }
      ],
      style: [
        {
          innerHTML: `@keyframes loading-dash {
            0% {
              stroke-dasharray: 1, 200;
              stroke-dashoffset: 0;
            }
            50% {
                stroke-dasharray: 90, 150;
                stroke-dashoffset: -40px;
            }
            100% {
                stroke-dasharray: 90, 150;
                stroke-dashoffset: -120px;
            }
          }`,
          type: 'text/css'
        }
      ],
      script: [
        { innerHTML: `function setupWebViewJavascriptBridge (callback) {
          if (window.navigator.userAgent.toLocaleLowerCase().indexOf('k2ex_android') > -1) {
            if (window.WebViewJavascriptBridge) {
              callback(WebViewJavascriptBridge)
            } else {
              document.addEventListener('WebViewJavascriptBridgeReady', function () { callback(WebViewJavascriptBridge) }, false)
            }
          } else {
            if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
            if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
            window.WVJBCallbacks = [callback];
            var WVJBIframe = document.createElement('iframe');
            WVJBIframe.style.display = 'none';
            WVJBIframe.src = 'https://__bridge_loaded__';
            document.documentElement.appendChild(WVJBIframe);
            setTimeout(function () { document.documentElement.removeChild(WVJBIframe) }, 0)
          }
        }
        function connectWebViewJavascriptBridge (callback) {
          if (window.WebViewJavascriptBridge) {
            callback(WebViewJavascriptBridge)
          } else {
            document.addEventListener('WebViewJavascriptBridgeReady', function () { callback(WebViewJavascriptBridge) }, false)
          }
        }`, type: 'text/javascript' },
        {
          src: 'https://res.ktx.com/common/js/gt4.js',
          type: 'text/javascript'
        },
        {
          id: 'ze-snippet',
          src: 'https://static.zdassets.com/ekr/snippet.js?key=a1a592e0-19ab-4309-8a5d-0aa582ee1a69',
          type: 'text/javascript'
        },
        {
          innerHTML: `(function (para) {
            var p = para.sdk_url, n = para.name, w = window, d = document, s = 'script', x = null, y = null;
            if (typeof (w['sensorsDataAnalytic201505']) !== 'undefined') {
              return false;
            }
            w['sensorsDataAnalytic201505'] = n;
            w[n] = w[n] || function (a) { return function () { (w[n]._q = w[n]._q || []).push([a, arguments]); } };
            var ifs = ['track', 'quick', 'register', 'registerPage', 'registerOnce', 'clearAllRegister', 'trackSignup', 'trackAbtest', 'setProfile', 'setOnceProfile', 'appendProfile', 'incrementProfile', 'deleteProfile', 'unsetProfile', 'identify', 'login', 'logout', 'trackLink'];
            for (var i = 0; i < ifs.length; i++) {
              w[n][ifs[i]] = w[n].call(null, ifs[i]);
            }
            if (!w[n]._t) {
              x = d.createElement(s), y = d.getElementsByTagName(s)[0];
              x.async = 1;
              x.src = p;
              x.setAttribute('charset', 'UTF-8');
              w[n].para = para;
              y.parentNode.insertBefore(x, y);
            }
          })({
            sdk_url: 'https://res.ktx.com/common/js/sensorsdata.min.js',
            name: 'sensors',
            // todowb
            server_url: 'https://api.ktx.com/user/event/product', // 正式环境
            // server_url: 'https://ma-tapi.tonetou.com/user/event/product', // 测试环境
            is_track_single_page: true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
            use_client_time: true,
            show_log: false,
            app_js_bridge: true,
            send_type: 'beacon',
            heatmap: {
              //是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
              clickmap: 'default',
              //是否开启触达注意力图，not_collect 表示关闭，不会自动采集 $WebStay 事件，可以设置 'default' 表示开启。
              scroll_notice_map: 'default'
            },
            is_track_device_id: true
          });
          sensors.registerPage({
            current_device:getCookieItem('KTX_device_id')
          });
          // sensors.quick('autoTrack');`
          , type: 'text/javascript'
        }
      ],
      link: [
        {
          rel:"icon", type:"image/x-icon", href: BASE_URL_DIRECTORY + "favicon.ico"
        }
      ]
    }
  },

  css: [
    'element-plus/dist/index.css',
    'element-plus/theme-chalk/display.css',
    '/assets/style/element-theme.scss',
    '/assets/style/reset.scss',
    '/assets/style/global.scss',
    '/assets/style/class.scss'
  ],

  srcDir: './src',
  compatibilityDate: '2024-09-29'
})